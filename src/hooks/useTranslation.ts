
import { useState } from 'react';
import { translations, Language, TranslationKey } from '../i18n/translations';

export const useTranslation = () => {
  const [language, setLanguage] = useState<Language>('en');

  const t = (key: TranslationKey): string => {
    return translations[language][key] || key;
  };

  const changeLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('language', lang);
  };

  // Initialize language from localStorage
  useState(() => {
    const savedLang = localStorage.getItem('language') as Language;
    if (savedLang && translations[savedLang]) {
      setLanguage(savedLang);
    }
  });

  return { t, language, changeLanguage };
};
