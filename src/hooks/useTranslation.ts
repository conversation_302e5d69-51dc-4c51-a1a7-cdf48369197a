
import { useState, useEffect } from 'react';
import { translations, Language, TranslationKey } from '../i18n/translations';

export const useTranslation = () => {
  const [language, setLanguage] = useState<Language>(() => {
    // Initialize from localStorage or default to 'en'
    const savedLang = localStorage.getItem('language') as Language;
    return (savedLang && translations[savedLang]) ? savedLang : 'en';
  });

  const t = (key: TranslationKey): string => {
    return translations[language][key] || key;
  };

  const changeLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('language', lang);
  };

  // Ensure language is persisted when component mounts
  useEffect(() => {
    const savedLang = localStorage.getItem('language') as Language;
    if (savedLang && translations[savedLang] && savedLang !== language) {
      setLanguage(savedLang);
    }
  }, []);

  return { t, language, changeLanguage };
};
