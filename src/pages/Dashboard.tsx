
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, MessageSquare, Users, TrendingUp, Clock, Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';

const Dashboard = () => {
  const { t } = useTranslation();

  const stats = [
    {
      title: t('totalVisitors'),
      value: '12,847',
      change: '+12.5%',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: t('activeProjects'),
      value: '8',
      change: '+2',
      icon: Bot,
      color: 'text-green-600'
    },
    {
      title: t('totalChats'),
      value: '3,247',
      change: '+18.2%',
      icon: MessageSquare,
      color: 'text-purple-600'
    },
    {
      title: t('satisfactionRate'),
      value: '94.2%',
      change: '+2.1%',
      icon: TrendingUp,
      color: 'text-orange-600'
    }
  ];

  const recentActivities = [
    { time: '2分钟前', action: '新用户注册', details: '<EMAIL>' },
    { time: '15分钟前', action: '项目创建', details: '电商客服机器人' },
    { time: '1小时前', action: '知识库更新', details: '添加了50个新的FAQ' },
    { time: '2小时前', action: '对话完成', details: '客服机器人处理了用户咨询' }
  ];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">{t('welcomeBack')}</h1>
            <p className="text-gray-600 mt-1">这里是您的AI客服平台概览</p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <p className="text-xs text-green-600 mt-1">
                  {stat.change} 较上月
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                {t('recentActivity')}
              </CardTitle>
              <CardDescription>最近的系统活动记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between gap-2">
                        <span className="font-medium text-gray-900 truncate">{activity.action}</span>
                        <span className="text-sm text-gray-500 flex-shrink-0">{activity.time}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1 truncate">{activity.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
