
import { <PERSON><PERSON>hart<PERSON>, <PERSON><PERSON>, MessageSquare, Users, TrendingUp, Clock, <PERSON>ting<PERSON>, ThumbsUp } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Tooltip,
  Legend
} from 'recharts';

const Dashboard = () => {
  const { t } = useTranslation();

  const stats = [
    {
      title: t('totalVisitors'),
      value: '12,847',
      change: '+12.5%',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: t('activeProjects'),
      value: '8',
      change: '+2',
      icon: Bo<PERSON>,
      color: 'text-green-600'
    },
    {
      title: t('totalChats'),
      value: '3,247',
      change: '+18.2%',
      icon: MessageSquare,
      color: 'text-purple-600'
    },
    {
      title: t('satisfactionRate'),
      value: '94.2%',
      change: '****%',
      icon: TrendingUp,
      color: 'text-orange-600'
    }
  ];

  const recentActivities = [
    { time: '2分钟前', action: '新用户注册', details: '<EMAIL>' },
    { time: '15分钟前', action: '项目创建', details: '电商客服机器人' },
    { time: '1小时前', action: '知识库更新', details: '添加了50个新的FAQ' },
    { time: '2小时前', action: '对话完成', details: '客服机器人处理了用户咨询' }
  ];

  // Chart data
  const visitorTrendData = [
    { date: '07-01', visitors: 245, chats: 89 },
    { date: '07-02', visitors: 312, chats: 124 },
    { date: '07-03', visitors: 189, chats: 67 },
    { date: '07-04', visitors: 398, chats: 156 },
    { date: '07-05', visitors: 267, chats: 98 },
    { date: '07-06', visitors: 445, chats: 178 },
    { date: '07-07', visitors: 356, chats: 134 },
  ];

  const npsData = [
    { name: '推荐者', value: 65, color: '#10B981' },
    { name: '中性者', value: 25, color: '#F59E0B' },
    { name: '批评者', value: 10, color: '#EF4444' },
  ];

  const chartConfig = {
    visitors: {
      label: "访客数",
      color: "#3B82F6",
    },
    chats: {
      label: "对话数",
      color: "#10B981",
    },
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">{t('welcomeBack')}</h1>
            <p className="text-gray-600 mt-1">这里是您的AI客服平台概览</p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <p className="text-xs text-green-600 mt-1">
                  {stat.change} 较上月
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Visitor Trend Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                访客咨询趋势
              </CardTitle>
              <CardDescription>过去7天的访客和对话数据</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={visitorTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line
                      type="monotone"
                      dataKey="visitors"
                      stroke="var(--color-visitors)"
                      strokeWidth={2}
                      name="访客数"
                    />
                    <Line
                      type="monotone"
                      dataKey="chats"
                      stroke="var(--color-chats)"
                      strokeWidth={2}
                      name="对话数"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* NPS Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ThumbsUp className="w-5 h-5" />
                用户满意度(NPS)
              </CardTitle>
              <CardDescription>净推荐值分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={npsData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {npsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 space-y-2">
                {npsData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: item.color }}
                      />
                      {item.name}
                    </div>
                    <span className="font-medium">{item.value}%</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              {t('recentActivity')}
            </CardTitle>
            <CardDescription>最近的系统活动记录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between gap-2">
                      <span className="font-medium text-gray-900 truncate">{activity.action}</span>
                      <span className="text-sm text-gray-500 flex-shrink-0">{activity.time}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1 truncate">{activity.details}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
