
import { useState } from 'react';
import { Plus, Search, MoreHorizontal, Edit, Trash2, Eye, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslation } from '@/hooks/useTranslation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ProjectWizard } from '@/components/ProjectWizard';
import { ChatWidget } from '@/components/ChatWidget';

const Projects = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newProject, setNewProject] = useState({
    name: '',
    websiteUrl: '',
    description: ''
  });

  const projects = [
    {
      id: 1,
      name: '电商客服机器人',
      websiteUrl: 'https://shop.example.com',
      description: '为电商网站提供24/7客服支持',
      status: 'active',
      createdAt: '2024-01-15',
      chats: 245,
      satisfaction: 94.2
    },
    {
      id: 2,
      name: '技术支持助手',
      websiteUrl: 'https://support.example.com',
      description: '处理技术问题和故障排除',
      status: 'active',
      createdAt: '2024-01-10',
      chats: 128,
      satisfaction: 89.5
    },
    {
      id: 3,
      name: '销售咨询机器人',
      websiteUrl: 'https://sales.example.com',
      description: '协助销售流程和产品咨询',
      status: 'inactive',
      createdAt: '2024-01-05',
      chats: 67,
      satisfaction: 91.8
    }
  ];

  const handleCreateProject = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Creating project:', newProject);
    setIsCreateDialogOpen(false);
    setNewProject({ name: '', websiteUrl: '', description: '' });
  };

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{t('myProjects')}</h1>
            <p className="text-gray-600 mt-2">管理您的AI客服项目</p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                {t('createProject')}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t('createProject')}</DialogTitle>
                <DialogDescription>创建一个新的AI客服项目</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateProject} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="projectName">{t('projectName')}</Label>
                  <Input
                    id="projectName"
                    placeholder="请输入项目名称"
                    value={newProject.name}
                    onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="websiteUrl">{t('websiteUrl')}</Label>
                  <Input
                    id="websiteUrl"
                    type="url"
                    placeholder="https://your-website.com"
                    value={newProject.websiteUrl}
                    onChange={(e) => setNewProject({ ...newProject, websiteUrl: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">{t('description')}</Label>
                  <Input
                    id="description"
                    placeholder="项目描述"
                    value={newProject.description}
                    onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    {t('cancel')}
                  </Button>
                  <Button type="submit">{t('create')}</Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="搜索项目..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <CardDescription className="mt-1">{project.websiteUrl}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      project.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {project.status === 'active' ? t('active') : t('inactive')}
                    </span>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">{project.description}</p>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{project.chats}</div>
                    <div className="text-xs text-gray-500">总对话数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{project.satisfaction}%</div>
                    <div className="text-xs text-gray-500">满意度</div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="w-4 h-4 mr-1" />
                    {t('view')}
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Settings className="w-4 h-4 mr-1" />
                    设置
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">没有找到匹配的项目</div>
            <p className="text-gray-500">尝试调整搜索条件或创建新项目</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Projects;
