import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useTranslation } from '@/hooks/useTranslation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { useNavigate } from 'react-router-dom';
import { 
  Upload, 
  MessageSquare, 
  Plus, 
  X,
  Eye,
  Settings,
  Home,
  Monitor,
  ArrowLeft
} from 'lucide-react';

const NewProject = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('general');
  const [projectData, setProjectData] = useState({
    name: '',
    websiteUrl: '',
    description: '',
    widget: {
      assistantName: 'Hali',
      welcomeMessage: "Hi Hal<PERSON> here to assist with any questions you have. How can I help you today?",
      color: '#FF7A51',
      headerColor: '#FF7A51',
      position: 'bottom-right',
      logo: null,
      suggestedQuestions: ['No questions', 'How are you?', 'I have another question'],
      visibility: true,
      showOnAllPages: true,
      allowedDomains: [],
      countriesFilter: 'off',
      allowedCountries: [],
      forbiddenCountries: []
    }
  });

  const tabs = [
    { id: 'general', title: 'General', icon: Settings },
    { id: 'homescreen', title: 'Home Screen', icon: Home },
    { id: 'chat', title: 'Chat', icon: MessageSquare },
    { id: 'widget', title: 'Widget button', icon: Monitor },
    { id: 'visibility', title: 'Visibility', icon: Eye }
  ];

  const handleSave = () => {
    // TODO: Save project data
    console.log('Saving project:', projectData);
    navigate('/projects');
  };

  const addSuggestedQuestion = () => {
    setProjectData(prev => ({
      ...prev,
      widget: {
        ...prev.widget,
        suggestedQuestions: [...prev.widget.suggestedQuestions, '']
      }
    }));
  };

  const updateSuggestedQuestion = (index: number, value: string) => {
    setProjectData(prev => ({
      ...prev,
      widget: {
        ...prev.widget,
        suggestedQuestions: prev.widget.suggestedQuestions.map((q, i) => i === index ? value : q)
      }
    }));
  };

  const removeSuggestedQuestion = (index: number) => {
    setProjectData(prev => ({
      ...prev,
      widget: {
        ...prev.widget,
        suggestedQuestions: prev.widget.suggestedQuestions.filter((_, i) => i !== index)
      }
    }));
  };

  return (
    <DashboardLayout>
      <div className="flex-1 flex flex-col h-full">
        {/* Header */}
        <div className="p-6 border-b bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/projects')}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back</span>
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">New Project</h1>
                <p className="text-sm text-gray-600 mt-1">
                  Adjust the appearance of your chat widget to match your website's style.
                </p>
              </div>
            </div>
            <Button 
              onClick={handleSave}
              style={{ backgroundColor: projectData.widget.color }}
              className="text-white hover:opacity-90"
            >
              Save
            </Button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Left Panel - Configuration */}
          <div className="flex-1 flex flex-col">
            {/* Tabs */}
            <div className="border-b bg-white">
              <div className="flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {tab.title}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
              <div className="max-w-2xl">
                {/* General Tab */}
                {activeTab === 'general' && (
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="assistantName" className="text-sm font-medium">Name</Label>
                      <Input
                        id="assistantName"
                        value={projectData.widget.assistantName}
                        onChange={(e) => setProjectData(prev => ({
                          ...prev,
                          widget: { ...prev.widget, assistantName: e.target.value }
                        }))}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Logo</Label>
                      <div className="mt-2 flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                            <span className="text-xs text-gray-500">
                              {projectData.widget.assistantName.charAt(0)}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center bg-white">
                            <Upload className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                            <p className="text-xs text-gray-500 mt-1">SVG, PNG, JPG or GIF (max. 1000x1000 px)</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Brand color</Label>
                      <div className="mt-2 flex items-center space-x-3">
                        <Input
                          value={projectData.widget.color}
                          onChange={(e) => setProjectData(prev => ({
                            ...prev,
                            widget: { ...prev.widget, color: e.target.value }
                          }))}
                          className="flex-1"
                        />
                        <div
                          className="w-8 h-8 rounded border"
                          style={{ backgroundColor: projectData.widget.color }}
                        />
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Header color</Label>
                      <p className="text-xs text-gray-500 mt-1">Customize the look of your chat window to match your brand.</p>

                      <div className="mt-4 space-y-3">
                        <div className="flex items-center space-x-3">
                          <input
                            type="radio"
                            id="default-color"
                            name="headerColor"
                            defaultChecked
                            className="text-blue-600"
                          />
                          <Label htmlFor="default-color" className="text-sm">Default (from the brand color scheme)</Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <input
                            type="radio"
                            id="custom-color"
                            name="headerColor"
                            className="text-blue-600"
                          />
                          <Label htmlFor="custom-color" className="text-sm">Custom (Pick a specific color to highlight the chat header)</Label>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Display beacon branding</Label>
                        <p className="text-xs text-gray-500 mt-1">Show the Powered by Outpost link in your Web Chat.</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                )}

                {/* Home Screen Tab */}
                {activeTab === 'homescreen' && (
                  <div className="space-y-6">
                    <div>
                      <Label className="text-sm font-medium">Home Screen is disabled</Label>
                      <p className="text-xs text-gray-500 mt-1">
                        Upgrade your plan to unlock Home Screen and offer a welcoming experience to
                        your visitors. <a href="#" className="text-blue-600 hover:underline">Learn more</a>
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="welcomeMessage" className="text-sm font-medium">Welcome message</Label>
                      <p className="text-xs text-gray-500 mt-1">Set the first message users see when they open Chat.</p>
                      <Textarea
                        id="welcomeMessage"
                        value={projectData.widget.welcomeMessage}
                        onChange={(e) => setProjectData(prev => ({
                          ...prev,
                          widget: { ...prev.widget, welcomeMessage: e.target.value }
                        }))}
                        className="mt-2"
                        rows={3}
                        placeholder="We are here to help"
                      />
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Background</Label>
                      <p className="text-xs text-gray-500 mt-1">Customize the background to fit your brand's spirit.</p>

                      <div className="mt-4 space-y-3">
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="color-bg" name="background" defaultChecked />
                          <Label htmlFor="color-bg" className="text-sm">Color</Label>
                        </div>
                        <div className="ml-6">
                          <div className="w-8 h-8 bg-blue-500 rounded border"></div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="gradient-bg" name="background" />
                          <Label htmlFor="gradient-bg" className="text-sm">Gradient</Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="image-bg" name="background" />
                          <Label htmlFor="image-bg" className="text-sm">Image</Label>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Translations</Label>
                      <p className="text-xs text-gray-500 mt-1">
                        Welcome to our support bot. Please click on "Start conversation" and
                        we will make sure to assist you.
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Start button text</Label>
                      <Input
                        defaultValue="Start conversation"
                        className="mt-2"
                      />
                    </div>
                  </div>
                )}

                {/* Chat Tab */}
                {activeTab === 'chat' && (
                  <div className="space-y-6">
                    <div>
                      <Label className="text-sm font-medium">Chat style</Label>
                      <Input
                        defaultValue="Bubbles"
                        className="mt-2"
                        readOnly
                      />
                    </div>

                    <div>
                      <Label htmlFor="welcomeMessage2" className="text-sm font-medium">Welcome message</Label>
                      <p className="text-xs text-gray-500 mt-1">Set the first message users see when they open Chat.</p>
                      <div className="mt-2 space-y-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm">🎉</span>
                          <span className="text-sm">Welcome</span>
                        </div>
                        <Textarea
                          value={projectData.widget.welcomeMessage}
                          onChange={(e) => setProjectData(prev => ({
                            ...prev,
                            widget: { ...prev.widget, welcomeMessage: e.target.value }
                          }))}
                          className="mt-2"
                          rows={3}
                          placeholder="Hi Hali here to assist with any questions you have. How can I help you today?"
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium">Suggested Questions</Label>
                          <p className="text-xs text-gray-500 mt-1">Help visitors start a conversation by showing quick start options.</p>
                        </div>
                        <Switch defaultChecked />
                      </div>

                      <div className="mt-4 space-y-3">
                        {projectData.widget.suggestedQuestions.map((question, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Input
                              value={question}
                              onChange={(e) => updateSuggestedQuestion(index, e.target.value)}
                              placeholder="Enter question"
                              className="flex-1"
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeSuggestedQuestion(index)}
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={addSuggestedQuestion}
                          className="flex items-center space-x-2"
                        >
                          <Plus className="w-4 h-4" />
                          <span>Add question</span>
                        </Button>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Break between follow-up messages</Label>
                      <p className="text-xs text-gray-500 mt-1">
                        Select how long after each message to begin showing "Typing" before next message
                        is sent (seconds).
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Link Behavior</Label>
                      <p className="text-xs text-gray-500 mt-1">Choose how links open in your product.</p>

                      <div className="mt-4 space-y-3">
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="new-tab" name="linkBehavior" defaultChecked />
                          <Label htmlFor="new-tab" className="text-sm">New Tab (Recommended for most products)</Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="same-tab" name="linkBehavior" />
                          <Label htmlFor="same-tab" className="text-sm">Same Tab (Recommended for your product)</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Widget Button Tab */}
                {activeTab === 'widget' && (
                  <div className="space-y-6">
                    <div>
                      <Label className="text-sm font-medium">Button position</Label>
                      <p className="text-xs text-gray-500 mt-1">Choose whether the chat button appears on the left or right side of your site.</p>

                      <div className="mt-4 space-y-3">
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="right-pos" name="position" defaultChecked />
                          <Label htmlFor="right-pos" className="text-sm">Right</Label>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Button style</Label>
                      <p className="text-xs text-gray-500 mt-1">Adjust the button size to fit your layout.</p>

                      <div className="mt-4 space-y-3">
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="small-btn" name="buttonStyle" />
                          <Label htmlFor="small-btn" className="text-sm">Small (40 px) (Compact size to save screen space)</Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="medium-btn" name="buttonStyle" defaultChecked />
                          <Label htmlFor="medium-btn" className="text-sm">Medium (60 px) (Balanced size for visibility and usability - default option)</Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <input type="radio" id="large-btn" name="buttonStyle" />
                          <Label htmlFor="large-btn" className="text-sm">Large (80 px) (More prominent for high engagement)</Label>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">One-Page Message</Label>
                      <p className="text-xs text-gray-500 mt-1">
                        Create one welcome message with automated chat popups. These messages
                        appear to visitors to encourage users and increase conversions.
                      </p>
                    </div>

                    <div>
                      <Label className="text-sm font-medium">Custom Widget icon</Label>
                      <p className="text-xs text-gray-500 mt-1">Make your chatbot feel like part of your brand by uploading a custom widget.</p>

                      <div className="mt-4 flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                            <span className="text-xs text-gray-500">
                              {projectData.widget.assistantName.charAt(0)}
                            </span>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center bg-white">
                            <Upload className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                            <p className="text-xs text-gray-500 mt-1">PNG, JPG, GIF or SVG</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Visibility Tab */}
                {activeTab === 'visibility' && (
                  <div className="space-y-6">
                    <div>
                      <Label className="text-sm font-medium">Allowed domains</Label>
                      <p className="text-xs text-gray-500 mt-1">
                        Limit access to your Web Chat widget to specific domains. Press Enter to add
                        another domain. Use an asterisk for wildcard domains (e.g., "*.example.ai").
                      </p>

                      <div className="mt-4">
                        <Input
                          placeholder="Add a domain here, e.g., *.example.ai"
                          className="mb-2"
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium">Countries filter</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">Off</span>
                          <Switch />
                        </div>
                      </div>

                      <div className="mt-4 space-y-3">
                        <div>
                          <Label className="text-sm font-medium">Allowed countries</Label>
                        </div>
                        <div>
                          <Label className="text-sm font-medium">Forbidden countries</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Panel - Preview */}
          <div className="w-96 border-l bg-white flex flex-col">
            <div className="p-4 border-b">
              <h3 className="font-medium text-gray-900">Preview</h3>
            </div>

            <div className="flex-1 p-4 bg-gray-50 relative">
              {/* Mobile Preview Frame */}
              <div className="bg-white rounded-lg shadow-lg mx-auto" style={{ width: '280px', height: '500px' }}>
                {/* Chat Widget Preview */}
                <div className="relative h-full">
                  {/* Chat Button */}
                  <div
                    className="absolute bottom-4 right-4 w-14 h-14 rounded-full shadow-lg flex items-center justify-center cursor-pointer"
                    style={{ backgroundColor: projectData.widget.color }}
                  >
                    <MessageSquare className="w-6 h-6 text-white" />
                  </div>

                  {/* Chat Window (when expanded) */}
                  <div className="absolute bottom-20 right-4 w-64 h-80 bg-white rounded-lg shadow-xl border overflow-hidden">
                    {/* Header */}
                    <div
                      className="p-4 text-white"
                      style={{ backgroundColor: projectData.widget.headerColor }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                          <span className="text-xs font-medium">
                            {projectData.widget.assistantName.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <h4 className="font-medium text-sm">{projectData.widget.assistantName}</h4>
                          <p className="text-xs opacity-90">We are here to help</p>
                        </div>
                      </div>
                    </div>

                    {/* Chat Content */}
                    <div className="p-4 flex-1">
                      <div className="space-y-3">
                        {/* Welcome Message */}
                        <div className="flex items-start space-x-2">
                          <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-xs">
                              {projectData.widget.assistantName.charAt(0)}
                            </span>
                          </div>
                          <div className="bg-gray-100 rounded-lg p-2 max-w-xs">
                            <p className="text-xs text-gray-800">
                              {projectData.widget.welcomeMessage}
                            </p>
                          </div>
                        </div>

                        {/* Suggested Questions */}
                        {activeTab === 'chat' && projectData.widget.suggestedQuestions.length > 0 && (
                          <div className="space-y-2">
                            {projectData.widget.suggestedQuestions.slice(0, 3).map((question, index) => (
                              <button
                                key={index}
                                className="block w-full text-left p-2 text-xs bg-white border border-gray-200 rounded-lg hover:bg-gray-50"
                              >
                                {question || `Question ${index + 1}`}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Input Area */}
                    <div className="p-3 border-t">
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          placeholder="Type your message..."
                          className="flex-1 text-xs p-2 border border-gray-200 rounded-lg"
                          readOnly
                        />
                        <button
                          className="p-2 rounded-lg text-white"
                          style={{ backgroundColor: projectData.widget.color }}
                        >
                          <span className="text-xs">→</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default NewProject;
