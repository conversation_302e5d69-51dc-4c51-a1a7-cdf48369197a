
import { useState } from 'react';
import { Plus, Search, Upload, Link, HelpCircle, FileText, Trash2, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useTranslation } from '@/hooks/useTranslation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';

const KnowledgeBase = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDocumentOpen, setIsAddDocumentOpen] = useState(false);
  const [isAddUrlOpen, setIsAddUrlOpen] = useState(false);
  const [isAddFaqOpen, setIsAddFaqOpen] = useState(false);
  const [newFaq, setNewFaq] = useState({ question: '', answer: '' });
  const [newUrl, setNewUrl] = useState('');

  const documents = [
    {
      id: 1,
      title: '产品使用手册.pdf',
      type: 'file',
      size: '2.5 MB',
      uploadedAt: '2024-01-15',
      status: 'processed'
    },
    {
      id: 2,
      title: '常见问题解答',
      type: 'faq',
      count: 25,
      updatedAt: '2024-01-14',
      status: 'active'
    },
    {
      id: 3,
      title: 'https://help.example.com',
      type: 'url',
      pages: 12,
      crawledAt: '2024-01-13',
      status: 'processed'
    }
  ];

  const handleAddFaq = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Adding FAQ:', newFaq);
    setIsAddFaqOpen(false);
    setNewFaq({ question: '', answer: '' });
  };

  const handleAddUrl = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Adding URL:', newUrl);
    setIsAddUrlOpen(false);
    setNewUrl('');
  };

  const filteredDocuments = documents.filter(doc =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{t('knowledgeBase')}</h1>
            <p className="text-gray-600 mt-2">管理AI机器人的知识库内容</p>
          </div>
          <div className="flex space-x-2">
            <Dialog open={isAddDocumentOpen} onOpenChange={setIsAddDocumentOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Upload className="w-4 h-4" />
                  {t('uploadFile')}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t('uploadFile')}</DialogTitle>
                  <DialogDescription>上传文档到知识库</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600">点击选择文件或拖拽文件到此处</p>
                    <p className="text-xs text-gray-500 mt-1">支持PDF、DOC、TXT等格式</p>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsAddDocumentOpen(false)}>
                      {t('cancel')}
                    </Button>
                    <Button>{t('uploadFile')}</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog open={isAddUrlOpen} onOpenChange={setIsAddUrlOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Link className="w-4 h-4" />
                  {t('addUrl')}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t('addUrl')}</DialogTitle>
                  <DialogDescription>添加网站链接到知识库</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleAddUrl} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="url">网站URL</Label>
                    <Input
                      id="url"
                      type="url"
                      placeholder="https://your-website.com"
                      value={newUrl}
                      onChange={(e) => setNewUrl(e.target.value)}
                      required
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsAddUrlOpen(false)}>
                      {t('cancel')}
                    </Button>
                    <Button type="submit">添加URL</Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>

            <Dialog open={isAddFaqOpen} onOpenChange={setIsAddFaqOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  {t('addFaq')}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{t('addFaq')}</DialogTitle>
                  <DialogDescription>添加常见问题到知识库</DialogDescription>
                </DialogHeader>
                <form onSubmit={handleAddFaq} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="question">问题</Label>
                    <Input
                      id="question"
                      placeholder="请输入问题"
                      value={newFaq.question}
                      onChange={(e) => setNewFaq({ ...newFaq, question: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="answer">答案</Label>
                    <Input
                      id="answer"
                      placeholder="请输入答案"
                      value={newFaq.answer}
                      onChange={(e) => setNewFaq({ ...newFaq, answer: e.target.value })}
                      required
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsAddFaqOpen(false)}>
                      {t('cancel')}
                    </Button>
                    <Button type="submit">添加FAQ</Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="搜索知识库..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                文档资料
              </CardTitle>
              <CardDescription>已上传的文档和网页内容</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredDocuments.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                        {doc.type === 'file' && <FileText className="w-4 h-4 text-blue-600" />}
                        {doc.type === 'url' && <Link className="w-4 h-4 text-blue-600" />}
                        {doc.type === 'faq' && <HelpCircle className="w-4 h-4 text-blue-600" />}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{doc.title}</p>
                        <p className="text-xs text-gray-500">
                          {doc.type === 'file' && `${doc.size} • ${doc.uploadedAt}`}
                          {doc.type === 'url' && `${doc.pages} 页面 • ${doc.crawledAt}`}
                          {doc.type === 'faq' && `${doc.count} 个问题 • ${doc.updatedAt}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        doc.status === 'processed' || doc.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {doc.status === 'processed' ? '已处理' : 
                         doc.status === 'active' ? '活跃' : '处理中'}
                      </span>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default KnowledgeBase;
