
import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { 
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  MessageSquare, 
  Clock,
  ThumbsUp,
  Download,
  Calendar
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

const Analytics = () => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState('7d');

  // 模拟数据
  const conversationData = [
    { date: '07-01', conversations: 45, resolved: 42, satisfaction: 4.2 },
    { date: '07-02', conversations: 52, resolved: 48, satisfaction: 4.5 },
    { date: '07-03', conversations: 38, resolved: 36, satisfaction: 4.1 },
    { date: '07-04', conversations: 61, resolved: 58, satisfaction: 4.6 },
    { date: '07-05', conversations: 49, resolved: 45, satisfaction: 4.3 },
    { date: '07-06', conversations: 55, resolved: 52, satisfaction: 4.7 },
    { date: '07-07', conversations: 43, resolved: 41, satisfaction: 4.4 },
  ];

  const responseTimeData = [
    { time: '00:00', avgTime: 2.1 },
    { time: '04:00', avgTime: 1.8 },
    { time: '08:00', avgTime: 3.2 },
    { time: '12:00', avgTime: 4.1 },
    { time: '16:00', avgTime: 3.8 },
    { time: '20:00', avgTime: 2.9 },
  ];

  const topicData = [
    { name: '产品咨询', value: 35, color: '#3B82F6' },
    { name: '技术支持', value: 28, color: '#10B981' },
    { name: '订单问题', value: 22, color: '#F59E0B' },
    { name: '退款申请', value: 15, color: '#EF4444' },
  ];

  const chartConfig = {
    conversations: {
      label: "对话数量",
      color: "#3B82F6",
    },
    resolved: {
      label: "已解决",
      color: "#10B981",
    },
    satisfaction: {
      label: "满意度",
      color: "#F59E0B",
    },
    avgTime: {
      label: "平均响应时间",
      color: "#8B5CF6",
    },
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <BarChart3 className="mr-2 h-6 w-6" />
              数据分析
            </h1>
            <p className="text-gray-600 mt-1">客服系统性能分析和洞察</p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <Calendar className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7天</SelectItem>
                <SelectItem value="30d">30天</SelectItem>
                <SelectItem value="90d">90天</SelectItem>
                <SelectItem value="1y">1年</SelectItem>
              </SelectContent>
            </Select>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              导出报告
            </Button>
          </div>
        </div>

        {/* KPI 指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总对话数</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2,468</div>
              <div className="flex items-center text-xs text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                +12.5% 较上期
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">解决率</CardTitle>
              <ThumbsUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">94.2%</div>
              <div className="flex items-center text-xs text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                +2.1% 较上期
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均响应时间</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2.3s</div>
              <div className="flex items-center text-xs text-red-600">
                <TrendingUp className="mr-1 h-3 w-3 rotate-180" />
                -0.5s 较上期
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">用户满意度</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4.6/5</div>
              <div className="flex items-center text-xs text-green-600">
                <TrendingUp className="mr-1 h-3 w-3" />
                +0.3 较上期
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 对话趋势图 */}
          <Card>
            <CardHeader>
              <CardTitle>对话趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={conversationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="conversations" fill="var(--color-conversations)" />
                    <Bar dataKey="resolved" fill="var(--color-resolved)" />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* 响应时间分析 */}
          <Card>
            <CardHeader>
              <CardTitle>响应时间分析</CardTitle>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={responseTimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line 
                      type="monotone" 
                      dataKey="avgTime" 
                      stroke="var(--color-avgTime)" 
                      strokeWidth={2} 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>

        {/* 底部图表 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 问题类型分布 */}
          <Card>
            <CardHeader>
              <CardTitle>问题类型分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={topicData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {topicData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 space-y-2">
                {topicData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <div 
                        className="w-3 h-3 rounded-full mr-2" 
                        style={{ backgroundColor: item.color }}
                      />
                      {item.name}
                    </div>
                    <span className="font-medium">{item.value}%</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 满意度评分 */}
          <Card>
            <CardHeader>
              <CardTitle>满意度评分分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[5, 4, 3, 2, 1].map((score) => (
                  <div key={score} className="flex items-center space-x-2">
                    <span className="text-sm font-medium w-4">{score}</span>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${score === 5 ? 45 : score === 4 ? 35 : score === 3 ? 15 : score === 2 ? 3 : 2}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-8">
                      {score === 5 ? '45%' : score === 4 ? '35%' : score === 3 ? '15%' : score === 2 ? '3%' : '2%'}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 活跃时段 */}
          <Card>
            <CardHeader>
              <CardTitle>活跃时段分析</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  <p>峰值时段：12:00-14:00</p>
                  <p>低谷时段：04:00-06:00</p>
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {Array.from({ length: 24 }, (_, i) => (
                    <div
                      key={i}
                      className={`h-8 rounded text-xs flex items-center justify-center ${
                        i >= 12 && i <= 14 
                          ? 'bg-red-100 text-red-800' 
                          : i >= 4 && i <= 6 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {i.toString().padStart(2, '0')}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Analytics;
