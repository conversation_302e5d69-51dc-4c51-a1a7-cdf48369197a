import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/useTranslation';
import { 
  Globe, 
  Upload, 
  MessageSquare, 
  Code, 
  Plus, 
  X,
  Copy,
  Eye,
  Palette,
  MapPin
} from 'lucide-react';

interface ProjectWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (project: any) => void;
}

export const ProjectWizard = ({ isOpen, onClose, onComplete }: ProjectWizardProps) => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);
  const [projectData, setProjectData] = useState({
    name: '',
    websiteUrl: '',
    description: '',
    knowledgeBase: {
      documents: [],
      urls: [],
      faqs: []
    },
    widget: {
      assistantName: 'AI助手',
      welcomeMessage: '您好！我是您的AI助手，有什么可以帮助您的吗？',
      color: '#3B82F6',
      position: 'bottom-right',
      logo: null,
      suggestedQuestions: ['如何使用产品？', '价格是多少？', '如何联系客服？']
    }
  });

  const steps = [
    { id: 'website', title: t('stepWebsite'), icon: Globe },
    { id: 'knowledge', title: t('stepKnowledge'), icon: Upload },
    { id: 'widget', title: t('stepWidget'), icon: MessageSquare },
    { id: 'code', title: t('stepCode'), icon: Code }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    onComplete(projectData);
    onClose();
  };

  const addFaq = () => {
    setProjectData(prev => ({
      ...prev,
      knowledgeBase: {
        ...prev.knowledgeBase,
        faqs: [...prev.knowledgeBase.faqs, { question: '', answer: '' }]
      }
    }));
  };

  const updateFaq = (index: number, field: 'question' | 'answer', value: string) => {
    setProjectData(prev => ({
      ...prev,
      knowledgeBase: {
        ...prev.knowledgeBase,
        faqs: prev.knowledgeBase.faqs.map((faq, i) => 
          i === index ? { ...faq, [field]: value } : faq
        )
      }
    }));
  };

  const removeFaq = (index: number) => {
    setProjectData(prev => ({
      ...prev,
      knowledgeBase: {
        ...prev.knowledgeBase,
        faqs: prev.knowledgeBase.faqs.filter((_, i) => i !== index)
      }
    }));
  };

  const generateEmbedCode = () => {
    return `<script>
  (function() {
    var script = document.createElement('script');
    script.src = 'https://widget.ai-customer-service.com/widget.js';
    script.setAttribute('data-project-id', '${Date.now()}');
    script.setAttribute('data-assistant-name', '${projectData.widget.assistantName}');
    script.setAttribute('data-color', '${projectData.widget.color}');
    script.setAttribute('data-position', '${projectData.widget.position}');
    document.head.appendChild(script);
  })();
</script>`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">{t('createProject')}</DialogTitle>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="flex items-center justify-between mb-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 
                ${index <= currentStep 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'border-gray-300 text-gray-400'
                }
              `}>
                <step.icon className="w-5 h-5" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                index <= currentStep ? 'text-blue-600' : 'text-gray-400'
              }`}>
                {step.title}
              </span>
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-4 ${
                  index < currentStep ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {/* Step 1: Website Information */}
          {currentStep === 0 && (
            <div className="space-y-6">
              <div>
                <Label htmlFor="projectName">{t('projectName')}</Label>
                <Input
                  id="projectName"
                  placeholder="请输入项目名称"
                  value={projectData.name}
                  onChange={(e) => setProjectData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="websiteUrl">{t('websiteUrl')}</Label>
                <Input
                  id="websiteUrl"
                  type="url"
                  placeholder={t('websiteUrlPlaceholder')}
                  value={projectData.websiteUrl}
                  onChange={(e) => setProjectData(prev => ({ ...prev, websiteUrl: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="description">{t('description')}</Label>
                <Textarea
                  id="description"
                  placeholder="项目描述"
                  value={projectData.description}
                  onChange={(e) => setProjectData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
            </div>
          )}

          {/* Step 2: Knowledge Base */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <Tabs defaultValue="upload" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="upload">{t('uploadDocument')}</TabsTrigger>
                  <TabsTrigger value="url">{t('addWebsiteUrl')}</TabsTrigger>
                  <TabsTrigger value="faq">{t('addFaqItem')}</TabsTrigger>
                </TabsList>
                
                <TabsContent value="upload" className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">拖拽文件到此处或点击上传</p>
                    <p className="text-sm text-gray-500 mt-2">支持 PDF, DOC, TXT 格式</p>
                    <Button variant="outline" className="mt-4">选择文件</Button>
                  </div>
                </TabsContent>
                
                <TabsContent value="url" className="space-y-4">
                  <div>
                    <Label>网站URL</Label>
                    <div className="flex gap-2">
                      <Input placeholder="https://example.com" />
                      <Button>添加</Button>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="faq" className="space-y-4">
                  <div className="space-y-4">
                    {projectData.knowledgeBase.faqs.map((faq, index) => (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <Label>FAQ #{index + 1}</Label>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => removeFaq(index)}
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </div>
                            <div>
                              <Label>{t('question')}</Label>
                              <Input
                                placeholder="输入问题"
                                value={faq.question}
                                onChange={(e) => updateFaq(index, 'question', e.target.value)}
                              />
                            </div>
                            <div>
                              <Label>{t('answer')}</Label>
                              <Textarea
                                placeholder="输入答案"
                                value={faq.answer}
                                onChange={(e) => updateFaq(index, 'answer', e.target.value)}
                              />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    <Button onClick={addFaq} variant="outline" className="w-full">
                      <Plus className="w-4 h-4 mr-2" />
                      添加FAQ
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* Step 3: Widget Settings */}
          {currentStep === 2 && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-6">
                <div>
                  <Label>{t('assistantName')}</Label>
                  <Input
                    value={projectData.widget.assistantName}
                    onChange={(e) => setProjectData(prev => ({
                      ...prev,
                      widget: { ...prev.widget, assistantName: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <Label>{t('welcomeMessage')}</Label>
                  <Textarea
                    value={projectData.widget.welcomeMessage}
                    onChange={(e) => setProjectData(prev => ({
                      ...prev,
                      widget: { ...prev.widget, welcomeMessage: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <Label>{t('widgetColor')}</Label>
                  <div className="flex gap-2">
                    <Input
                      type="color"
                      value={projectData.widget.color}
                      onChange={(e) => setProjectData(prev => ({
                        ...prev,
                        widget: { ...prev.widget, color: e.target.value }
                      }))}
                      className="w-16"
                    />
                    <Input
                      value={projectData.widget.color}
                      onChange={(e) => setProjectData(prev => ({
                        ...prev,
                        widget: { ...prev.widget, color: e.target.value }
                      }))}
                    />
                  </div>
                </div>
              </div>
              
              {/* Widget Preview */}
              <div className="bg-gray-100 rounded-lg p-4">
                <h3 className="font-medium mb-4">{t('widgetPreview')}</h3>
                <div className="bg-white rounded-lg shadow-lg p-4 max-w-sm">
                  <div className="flex items-center gap-2 mb-3">
                    <div 
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                      style={{ backgroundColor: projectData.widget.color }}
                    >
                      AI
                    </div>
                    <span className="font-medium">{projectData.widget.assistantName}</span>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-3 mb-3">
                    <p className="text-sm">{projectData.widget.welcomeMessage}</p>
                  </div>
                  <div className="space-y-2">
                    {projectData.widget.suggestedQuestions.map((question, index) => (
                      <button 
                        key={index}
                        className="w-full text-left text-sm p-2 border rounded hover:bg-gray-50"
                      >
                        {question}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Embed Code */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">{t('embedCode')}</h3>
                <p className="text-gray-600 mb-4">
                  将以下代码复制并粘贴到您网站的 &lt;head&gt; 标签中：
                </p>
                <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                  <pre>{generateEmbedCode()}</pre>
                </div>
                <Button 
                  onClick={() => copyToClipboard(generateEmbedCode())}
                  className="mt-4"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  {t('copyCode')}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <Button 
            variant="outline" 
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            {t('previous')}
          </Button>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              {t('cancel')}
            </Button>
            {currentStep < steps.length - 1 ? (
              <Button onClick={handleNext}>
                {t('next')}
              </Button>
            ) : (
              <Button onClick={handleComplete}>
                {t('finish')}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
