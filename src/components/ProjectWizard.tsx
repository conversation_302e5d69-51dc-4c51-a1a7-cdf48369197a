import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { useTranslation } from '@/hooks/useTranslation';
import { 
  Upload, 
  MessageSquare, 
  Plus, 
  X,
  Eye,
  Settings,
  Home,
  Monitor
} from 'lucide-react';

interface ProjectWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (project: any) => void;
}

export const ProjectWizard = ({ isOpen, onClose, onComplete }: ProjectWizardProps) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('general');
  const [projectData, setProjectData] = useState({
    name: '',
    websiteUrl: '',
    description: '',
    widget: {
      assistant<PERSON>ame: 'Hali',
      welcomeMessage: "Hi Hal<PERSON> here to assist with any questions you have. How can I help you today?",
      color: '#FF7A51',
      headerColor: '#FF7A51',
      position: 'bottom-right',
      logo: null,
      suggestedQuestions: ['No questions', 'How are you?', 'I have another question'],
      visibility: true,
      showOnAllPages: true
    }
  });

  const tabs = [
    { id: 'general', title: 'General', icon: Settings },
    { id: 'homescreen', title: 'Home Screen', icon: Home },
    { id: 'chat', title: 'Chat', icon: MessageSquare },
    { id: 'widget', title: 'Widget button', icon: Monitor },
    { id: 'visibility', title: 'Visibility', icon: Eye }
  ];

  const handleSave = () => {
    onComplete(projectData);
    onClose();
  };

  const addSuggestedQuestion = () => {
    setProjectData(prev => ({
      ...prev,
      widget: {
        ...prev.widget,
        suggestedQuestions: [...prev.widget.suggestedQuestions, '']
      }
    }));
  };

  const updateSuggestedQuestion = (index: number, value: string) => {
    setProjectData(prev => ({
      ...prev,
      widget: {
        ...prev.widget,
        suggestedQuestions: prev.widget.suggestedQuestions.map((q, i) => i === index ? value : q)
      }
    }));
  };

  const removeSuggestedQuestion = (index: number) => {
    setProjectData(prev => ({
      ...prev,
      widget: {
        ...prev.widget,
        suggestedQuestions: prev.widget.suggestedQuestions.filter((_, i) => i !== index)
      }
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden p-0">
        <div className="flex h-[90vh]">
          {/* Left Panel - Configuration */}
          <div className="flex-1 flex flex-col">
            <DialogHeader className="p-6 border-b">
              <DialogTitle className="text-xl">Appearance</DialogTitle>
              <p className="text-sm text-gray-600 mt-1">
                Adjust the appearance of your chat widget to match your website's style.
              </p>
            </DialogHeader>

            {/* Tabs */}
            <div className="border-b">
              <div className="flex space-x-8 px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {tab.title}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {/* General Tab */}
              {activeTab === 'general' && (
                <div className="space-y-6 max-w-md">
                  <div>
                    <Label htmlFor="assistantName" className="text-sm font-medium">Name</Label>
                    <Input
                      id="assistantName"
                      value={projectData.widget.assistantName}
                      onChange={(e) => setProjectData(prev => ({
                        ...prev,
                        widget: { ...prev.widget, assistantName: e.target.value }
                      }))}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Logo</Label>
                    <div className="mt-2 flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-xs text-gray-500">
                            {projectData.widget.assistantName.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                          <Upload className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">Click to upload or drag and drop</p>
                          <p className="text-xs text-gray-500 mt-1">SVG, PNG, JPG or GIF (max. 1000x1000 px)</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Brand color</Label>
                    <div className="mt-2 flex items-center space-x-3">
                      <Input
                        value={projectData.widget.color}
                        onChange={(e) => setProjectData(prev => ({
                          ...prev,
                          widget: { ...prev.widget, color: e.target.value }
                        }))}
                        className="flex-1"
                      />
                      <div 
                        className="w-8 h-8 rounded border"
                        style={{ backgroundColor: projectData.widget.color }}
                      />
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Header color</Label>
                    <p className="text-xs text-gray-500 mt-1">Customize the look of your chat window to match your brand.</p>
                  </div>
                </div>
              )}

              {/* Home Screen Tab */}
              {activeTab === 'homescreen' && (
                <div className="space-y-6 max-w-md">
                  <div>
                    <Label className="text-sm font-medium">Welcome message</Label>
                    <Textarea
                      value={projectData.widget.welcomeMessage}
                      onChange={(e) => setProjectData(prev => ({
                        ...prev,
                        widget: { ...prev.widget, welcomeMessage: e.target.value }
                      }))}
                      className="mt-2"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Suggested questions</Label>
                    <div className="mt-2 space-y-2">
                      {projectData.widget.suggestedQuestions.map((question, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <Input
                            value={question}
                            onChange={(e) => updateSuggestedQuestion(index, e.target.value)}
                            placeholder="Enter suggested question"
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSuggestedQuestion(index)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={addSuggestedQuestion}
                        className="w-full"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add suggested question
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Other tabs with placeholder content */}
              {activeTab === 'chat' && (
                <div className="space-y-6 max-w-md">
                  <div>
                    <Label className="text-sm font-medium">Chat settings</Label>
                    <p className="text-xs text-gray-500 mt-1">Configure chat behavior and responses.</p>
                  </div>
                </div>
              )}

              {activeTab === 'widget' && (
                <div className="space-y-6 max-w-md">
                  <div>
                    <Label className="text-sm font-medium">Widget button settings</Label>
                    <p className="text-xs text-gray-500 mt-1">Customize the chat widget button appearance.</p>
                  </div>
                </div>
              )}

              {activeTab === 'visibility' && (
                <div className="space-y-6 max-w-md">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Show widget</Label>
                      <p className="text-xs text-gray-500 mt-1">Control widget visibility</p>
                    </div>
                    <Switch
                      checked={projectData.widget.visibility}
                      onCheckedChange={(checked) => setProjectData(prev => ({
                        ...prev,
                        widget: { ...prev.widget, visibility: checked }
                      }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Show on all pages</Label>
                      <p className="text-xs text-gray-500 mt-1">Display widget on all website pages</p>
                    </div>
                    <Switch
                      checked={projectData.widget.showOnAllPages}
                      onCheckedChange={(checked) => setProjectData(prev => ({
                        ...prev,
                        widget: { ...prev.widget, showOnAllPages: checked }
                      }))}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Preview */}
          <div className="w-96 bg-gray-50 border-l flex flex-col">
            <div className="p-6 border-b bg-white">
              <h3 className="font-medium text-gray-900">Preview</h3>
              <p className="text-sm text-gray-500 mt-1">See how your widget will look</p>
            </div>

            <div className="flex-1 p-6 flex items-center justify-center">
              <div className="bg-white rounded-lg shadow-lg w-80 max-w-full">
                {/* Chat Header */}
                <div
                  className="p-4 rounded-t-lg"
                  style={{ backgroundColor: projectData.widget.color }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {projectData.widget.assistantName.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-white font-medium text-sm">
                        {projectData.widget.assistantName}
                      </h4>
                      <p className="text-white text-opacity-80 text-xs">Active now</p>
                    </div>
                  </div>
                </div>

                {/* Chat Content */}
                <div className="p-4 space-y-4">
                  {/* Welcome Message */}
                  <div className="flex items-start space-x-2">
                    <div
                      className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs"
                      style={{ backgroundColor: projectData.widget.color }}
                    >
                      {projectData.widget.assistantName.charAt(0)}
                    </div>
                    <div className="bg-gray-100 rounded-lg p-3 max-w-xs">
                      <p className="text-sm text-gray-800">
                        {projectData.widget.welcomeMessage}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">1 min ago</p>
                    </div>
                  </div>

                  {/* Suggested Questions */}
                  <div className="space-y-2">
                    {projectData.widget.suggestedQuestions.map((question, index) => (
                      <button
                        key={index}
                        className="w-full text-left text-sm p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        {question}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Chat Input */}
                <div className="p-4 border-t">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-100 rounded-lg px-3 py-2">
                      <p className="text-sm text-gray-500">Type a message...</p>
                    </div>
                    <button
                      className="p-2 rounded-lg text-white"
                      style={{ backgroundColor: projectData.widget.color }}
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Powered by */}
                <div className="px-4 pb-3">
                  <p className="text-xs text-gray-400 text-center">
                    Powered by <span className="font-medium">ChatGPT</span>
                  </p>
                </div>
              </div>
            </div>

            {/* Widget Button Preview */}
            <div className="p-6 border-t bg-white">
              <div className="relative">
                <div className="absolute bottom-4 right-4">
                  <button
                    className="w-14 h-14 rounded-full shadow-lg flex items-center justify-center text-white"
                    style={{ backgroundColor: projectData.widget.color }}
                  >
                    <MessageSquare className="w-6 h-6" />
                  </button>
                </div>
                <div className="h-20 bg-gray-100 rounded border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <span className="text-sm text-gray-500">Your website</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t p-6 bg-white">
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              style={{ backgroundColor: projectData.widget.color }}
              className="text-white hover:opacity-90"
            >
              Save
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
