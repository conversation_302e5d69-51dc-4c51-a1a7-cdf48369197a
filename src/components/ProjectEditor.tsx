import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { useTranslation } from '@/hooks/useTranslation';
import { 
  Settings, 
  Globe, 
  Upload, 
  MessageSquare, 
  Code, 
  Save,
  Eye,
  Trash2,
  Plus,
  X
} from 'lucide-react';

interface ProjectEditorProps {
  isOpen: boolean;
  onClose: () => void;
  project: any;
  onSave: (project: any) => void;
}

export const ProjectEditor = ({ isOpen, onClose, project, onSave }: ProjectEditorProps) => {
  const { t } = useTranslation();
  const [editedProject, setEditedProject] = useState(project || {});

  const handleSave = () => {
    onSave(editedProject);
    onClose();
  };

  const updateProject = (field: string, value: any) => {
    setEditedProject(prev => ({ ...prev, [field]: value }));
  };

  const updateWidget = (field: string, value: any) => {
    setEditedProject(prev => ({
      ...prev,
      widget: { ...prev.widget, [field]: value }
    }));
  };

  const addSuggestedQuestion = () => {
    const newQuestions = [...(editedProject.widget?.suggestedQuestions || []), ''];
    updateWidget('suggestedQuestions', newQuestions);
  };

  const updateSuggestedQuestion = (index: number, value: string) => {
    const newQuestions = [...(editedProject.widget?.suggestedQuestions || [])];
    newQuestions[index] = value;
    updateWidget('suggestedQuestions', newQuestions);
  };

  const removeSuggestedQuestion = (index: number) => {
    const newQuestions = (editedProject.widget?.suggestedQuestions || []).filter((_, i) => i !== index);
    updateWidget('suggestedQuestions', newQuestions);
  };

  if (!project) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            编辑项目 - {project.name}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="knowledge">知识库</TabsTrigger>
            <TabsTrigger value="widget">窗口设置</TabsTrigger>
            <TabsTrigger value="analytics">数据分析</TabsTrigger>
          </TabsList>

          {/* Basic Information */}
          <TabsContent value="basic" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  项目基本信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="projectName">{t('projectName')}</Label>
                  <Input
                    id="projectName"
                    value={editedProject.name || ''}
                    onChange={(e) => updateProject('name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="websiteUrl">{t('websiteUrl')}</Label>
                  <Input
                    id="websiteUrl"
                    type="url"
                    value={editedProject.websiteUrl || ''}
                    onChange={(e) => updateProject('websiteUrl', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="description">{t('description')}</Label>
                  <Textarea
                    id="description"
                    value={editedProject.description || ''}
                    onChange={(e) => updateProject('description', e.target.value)}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="status"
                    checked={editedProject.status === 'active'}
                    onCheckedChange={(checked) => updateProject('status', checked ? 'active' : 'inactive')}
                  />
                  <Label htmlFor="status">项目状态（启用/禁用）</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Knowledge Base */}
          <TabsContent value="knowledge" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  知识库管理
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <p>知识库管理功能</p>
                  <p className="text-sm">上传文档、添加网站链接、管理FAQ</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Widget Settings */}
          <TabsContent value="widget" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    窗口设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>{t('assistantName')}</Label>
                    <Input
                      value={editedProject.widget?.assistantName || ''}
                      onChange={(e) => updateWidget('assistantName', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label>{t('welcomeMessage')}</Label>
                    <Textarea
                      value={editedProject.widget?.welcomeMessage || ''}
                      onChange={(e) => updateWidget('welcomeMessage', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label>{t('widgetColor')}</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={editedProject.widget?.color || '#3B82F6'}
                        onChange={(e) => updateWidget('color', e.target.value)}
                        className="w-16"
                      />
                      <Input
                        value={editedProject.widget?.color || '#3B82F6'}
                        onChange={(e) => updateWidget('color', e.target.value)}
                      />
                    </div>
                  </div>
                  <div>
                    <Label>推荐问题</Label>
                    <div className="space-y-2">
                      {(editedProject.widget?.suggestedQuestions || []).map((question, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={question}
                            onChange={(e) => updateSuggestedQuestion(index, e.target.value)}
                            placeholder="输入推荐问题"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeSuggestedQuestion(index)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        variant="outline"
                        onClick={addSuggestedQuestion}
                        className="w-full"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        添加推荐问题
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Widget Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    实时预览
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-100 rounded-lg p-4">
                    <div className="bg-white rounded-lg shadow-lg p-4 max-w-sm">
                      <div className="flex items-center gap-2 mb-3">
                        <div 
                          className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
                          style={{ backgroundColor: editedProject.widget?.color || '#3B82F6' }}
                        >
                          AI
                        </div>
                        <span className="font-medium">
                          {editedProject.widget?.assistantName || 'AI助手'}
                        </span>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-3 mb-3">
                        <p className="text-sm">
                          {editedProject.widget?.welcomeMessage || '您好！我是您的AI助手，有什么可以帮助您的吗？'}
                        </p>
                      </div>
                      <div className="space-y-2">
                        {(editedProject.widget?.suggestedQuestions || []).map((question, index) => (
                          <button 
                            key={index}
                            className="w-full text-left text-sm p-2 border rounded hover:bg-gray-50"
                          >
                            {question}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics */}
          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>项目数据统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{project.chats}</div>
                    <div className="text-sm text-gray-500">总对话数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{project.satisfaction}%</div>
                    <div className="text-sm text-gray-500">满意度</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {project.status === 'active' ? '运行中' : '已停用'}
                    </div>
                    <div className="text-sm text-gray-500">状态</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{project.createdAt}</div>
                    <div className="text-sm text-gray-500">创建日期</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <Button variant="destructive" className="flex items-center gap-2">
            <Trash2 className="w-4 h-4" />
            删除项目
          </Button>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              {t('cancel')}
            </Button>
            <Button onClick={handleSave} className="flex items-center gap-2">
              <Save className="w-4 h-4" />
              {t('save')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
